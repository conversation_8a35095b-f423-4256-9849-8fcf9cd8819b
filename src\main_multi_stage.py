import pandas as pd
import argparse
import os
import random
import tqdm
from multi_stage_simulation import MultiStageSimulation

# 统一数据根路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def parse_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description='Run Multi-Stage European Parliament Voting Simulation.'
    )

    parser.add_argument(
        '--output', 
        type=str,
        default="./simulation_results/",
        help='The output path for simulation results.'
    )
    
    parser.add_argument(
        '--model',
        type=str,
        default="gpt-3.5-turbo",
        help="The model name for LLM API."
    )

    parser.add_argument(
        '--vote_list',
        nargs='*',
        default=[],
        help='List of vote IDs to simulate. If empty, will process all available votes.'
    )

    parser.add_argument(
        '--temperature',
        type=float,
        default=0.6,
        help='Temperature parameter for LLM generation.'
    )

    parser.add_argument(
        '--sampling_ratio',
        type=float,
        default=0.05,
        help='Sampling ratio for selecting speakers in stages 2 and 3 (default: 5%).'
    )

    parser.add_argument(
        '--single_vote',
        type=int,
        help='Run simulation for a single vote ID.'
    )

    return parser.parse_args()

def get_available_votes():
    """获取可用的投票ID列表"""
    votes_path = os.path.join(BASE_DIR, "data", "howTheyVoteDataSet", "votes.csv")
    speeches_path = os.path.join(BASE_DIR, "data", "debates", "counterfactual_speeches.csv")
    
    # 读取数据
    votes = pd.read_csv(votes_path)
    speeches = pd.read_csv(speeches_path)
    
    # 筛选2024年后的投票
    votes['timestamp'] = pd.to_datetime(votes['timestamp'], errors='coerce')
    cutoff_date = pd.Timestamp('2024-01-01')
    votes = votes[votes['timestamp'] >= cutoff_date]
    
    # 自动兼容缺失的列
    if "is_main" in votes.columns and "is_featured" in votes.columns:
        votes = votes[(votes["is_main"] == True) & (votes["is_featured"] == True)]
    
    # 取交集 - 只处理既有投票数据又有辩论发言的提案
    vote_ids = set(votes['id'])
    speech_ids = set(speeches['id'])
    common_ids = vote_ids & speech_ids
    
    # 返回可用的投票数据
    available_votes = votes[votes['id'].isin(common_ids)]
    return available_votes

def run_single_simulation(vote_id: int, vote_title: str, output_dir: str, temperature: float):
    """运行单个投票的模拟"""
    print(f"\n{'='*60}")
    print(f"开始模拟投票 ID: {vote_id}")
    print(f"投票标题: {vote_title}")
    print(f"{'='*60}")
    
    try:
        # 创建模拟实例
        simulation = MultiStageSimulation(
            vote_id=vote_id,
            vote_title=vote_title,
            output_dir=output_dir
        )
        
        # 运行完整模拟
        results = simulation.run_full_simulation(temperature=temperature)
        
        print(f"\n模拟完成！结果保存在: {simulation.proposal_dir}")
        
        # 打印简要统计
        print("\n=== 模拟结果统计 ===")
        total_votes = len(results)

        # 统计三个阶段的投票结果
        for stage_idx, stage_name in enumerate(['阶段一(初始)', '阶段二(党团)', '阶段三(最终)']):
            print(f"\n{stage_name}:")
            stage_votes = []
            for vote_list in results['llm_votes']:
                if isinstance(vote_list, list) and len(vote_list) > stage_idx:
                    stage_votes.append(vote_list[stage_idx])
                else:
                    stage_votes.append('UNKNOWN')

            # 统计各投票类型的数量
            vote_counts = {}
            for vote in stage_votes:
                vote_counts[vote] = vote_counts.get(vote, 0) + 1

            for vote_type in ['FOR', 'AGAINST', 'ABSTENTION']:
                count = vote_counts.get(vote_type, 0)
                percentage = (count / total_votes) * 100 if total_votes > 0 else 0
                print(f"  {vote_type}: {count} ({percentage:.1f}%)")

        # 与实际投票对比
        if 'actual_vote' in results.columns:
            actual_counts = results['actual_vote'].value_counts()
            print("\n=== 实际投票统计 ===")
            for vote_type in ['FOR', 'AGAINST', 'ABSTENTION']:
                count = actual_counts.get(vote_type, 0)
                percentage = (count / total_votes) * 100 if total_votes > 0 else 0
                print(f"  {vote_type}: {count} ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"模拟投票 {vote_id} 时出错: {e}")
        return False

def main():
    # 设置随机种子
    random.seed(42)
    
    # 解析参数
    args = parse_arguments()
    
    # 更新全局采样比例
    if hasattr(args, 'sampling_ratio'):
        import multi_stage_simulation
        multi_stage_simulation.SAMPLING_RATIO = args.sampling_ratio
    
    print("多阶段欧洲议会投票模拟系统")
    print(f"输出目录: {args.output}")
    print(f"LLM模型: {args.model}")
    print(f"温度参数: {args.temperature}")
    print(f"采样比例: {args.sampling_ratio * 100}%")
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 获取要处理的投票
    if args.single_vote:
        # 处理单个投票
        votes_path = os.path.join(BASE_DIR, "data", "howTheyVoteDataSet", "votes.csv")
        votes_df = pd.read_csv(votes_path)
        vote_row = votes_df[votes_df['id'] == args.single_vote]
        
        if vote_row.empty:
            print(f"未找到投票ID: {args.single_vote}")
            return
        
        vote_title = vote_row.iloc[0]['display_title']
        success = run_single_simulation(args.single_vote, vote_title, args.output, args.temperature)
        
        if success:
            print(f"\n✅ 投票 {args.single_vote} 模拟成功完成")
        else:
            print(f"\n❌ 投票 {args.single_vote} 模拟失败")
    
    else:
        # 处理多个投票
        available_votes = get_available_votes()
        
        if args.vote_list:
            # 处理指定的投票列表
            vote_id_ints = list(map(int, args.vote_list))
            votes_to_process = available_votes[available_votes['id'].isin(vote_id_ints)]
        else:
            # 处理所有可用投票
            votes_to_process = available_votes
        
        print(f"\n找到 {len(votes_to_process)} 个可处理的投票")
        
        if len(votes_to_process) == 0:
            print("没有找到可处理的投票数据")
            return
        
        # 处理每个投票
        successful_simulations = 0
        failed_simulations = 0
        
        for i, (_, row) in enumerate(tqdm.tqdm(votes_to_process.iterrows(), 
                                              total=len(votes_to_process),
                                              desc="处理投票")):
            vote_id = int(row['id'])
            vote_title = row['display_title']
            
            success = run_single_simulation(vote_id, vote_title, args.output, args.temperature)
            
            if success:
                successful_simulations += 1
            else:
                failed_simulations += 1
        
        # 打印总结
        print(f"\n{'='*60}")
        print("模拟完成总结")
        print(f"{'='*60}")
        print(f"✅ 成功模拟: {successful_simulations}")
        print(f"❌ 失败模拟: {failed_simulations}")
        print(f"📊 总计: {successful_simulations + failed_simulations}")
        
        if successful_simulations > 0:
            success_rate = (successful_simulations / (successful_simulations + failed_simulations)) * 100
            print(f"🎯 成功率: {success_rate:.1f}%")

if __name__ == "__main__":
    main()
