#!/usr/bin/env python3
"""
测试脚本：展示多阶段模拟中的详细调试信息
特别关注党团发言人和公开辩论发言人的详细信息
"""

import os
import sys
import pandas as pd

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from multi_stage_simulation import MultiStageSimulation

def test_debug_info():
    """测试调试信息的显示"""
    print("🧪 测试多阶段模拟的调试信息")
    print("=" * 60)
    
    # 使用一个较小的投票ID进行测试
    vote_id = 162202
    vote_title = "Extending the list of EU crimes to hate speech and hate crime"
    output_dir = "./test_debug_results/"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建模拟实例
        simulation = MultiStageSimulation(
            vote_id=vote_id,
            vote_title=vote_title,
            output_dir=output_dir
        )
        
        print(f"\n📊 模拟参数:")
        print(f"  投票ID: {vote_id}")
        print(f"  投票标题: {vote_title}")
        print(f"  采样比例: 5% (默认)")
        print(f"  输出目录: {output_dir}")
        
        # 计算总议员数
        total_agents = sum(len(group_df) for group_df in simulation.party_groups.values())
        print(f"  总议员数: {total_agents}")
        
        print(f"\n🚀 开始运行模拟...")
        print("注意观察以下调试信息:")
        print("1. 阶段一：第一个议员的请求体")
        print("2. 阶段二：党团发言人详细信息")
        print("3. 阶段三：公开辩论发言人详细信息")
        print("=" * 60)
        
        # 运行完整模拟
        results = simulation.run_full_simulation(temperature=0.6)
        
        print(f"\n✅ 模拟完成！")
        print(f"📁 结果保存在: {simulation.proposal_dir}")
        
        # 打印简要统计
        print(f"\n📊 模拟结果统计:")
        total_votes = len(results)

        # 统计三个阶段的投票结果
        for stage_idx, stage_name in enumerate(['阶段一(初始)', '阶段二(党团)', '阶段三(最终)']):
            print(f"\n{stage_name}:")
            stage_votes = []
            for vote_list in results['llm_votes']:
                if isinstance(vote_list, list) and len(vote_list) > stage_idx:
                    stage_votes.append(vote_list[stage_idx])
                else:
                    stage_votes.append('UNKNOWN')

            # 统计各投票类型的数量
            vote_counts = {}
            for vote in stage_votes:
                vote_counts[vote] = vote_counts.get(vote, 0) + 1

            for vote_type in ['FOR', 'AGAINST', 'ABSTENTION']:
                count = vote_counts.get(vote_type, 0)
                percentage = (count / total_votes) * 100 if total_votes > 0 else 0
                print(f"  {vote_type}: {count} ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_debug_info()
    if success:
        print(f"\n🎉 测试成功完成！")
        print(f"\n📝 调试信息说明:")
        print(f"- 阶段一显示第一个议员的完整请求体")
        print(f"- 阶段二显示党团内发言人的详细信息（姓名、国家、立场、推理、投票分布）")
        print(f"- 阶段三显示跨党团公开辩论发言人的详细信息")
        print(f"- 每个阶段都有进度条显示处理进度")
    else:
        print(f"\n❌ 测试失败，请检查错误信息")
